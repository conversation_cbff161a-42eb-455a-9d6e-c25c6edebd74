<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name=".IndianLawLibraryApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Indian_law_library">

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:label="@string/app_name" />

        <!-- Authentication Activities -->
        <activity
            android:name=".ui.auth.LoginActivity"
            android:exported="true"
            android:theme="@style/Theme.Indian_law_library.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.auth.SignUpActivity"
            android:exported="false"
            android:theme="@style/Theme.Indian_law_library.NoActionBar" />

        <activity
            android:name=".ui.auth.ForgotPasswordActivity"
            android:exported="false"
            android:theme="@style/Theme.Indian_law_library.NoActionBar" />

    </application>

</manifest>