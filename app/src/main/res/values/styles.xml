<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- Text Styles -->
    <style name="TextAppearance.App.Headline1" parent="TextAppearance.MaterialComponents.Headline1">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    
    <style name="TextAppearance.App.Headline2" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    
    <style name="TextAppearance.App.Headline3" parent="TextAppearance.MaterialComponents.Headline3">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    
    <style name="TextAppearance.App.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style>
    
    <style name="TextAppearance.App.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>
    
    <style name="TextAppearance.App.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textColor">@color/text_hint</item>
    </style>
    
    <!-- Button Styles -->
    <style name="Widget.App.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:letterSpacing">0.02</item>
    </style>
    
    <style name="Widget.App.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">@color/primary_blue</item>
        <item name="strokeColor">@color/primary_blue</item>
        <item name="strokeWidth">2dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">56dp</item>
    </style>
    
    <style name="Widget.App.Button.Text" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/primary_blue</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    
    <!-- TextInputLayout Styles -->
    <style name="Widget.App.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary_blue</item>
        <item name="hintTextColor">@color/primary_blue</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
    </style>
    
    <!-- Card Styles -->
    <style name="Widget.App.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/surface_light</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    
    <!-- Toolbar Styles -->
    <style name="Widget.App.Toolbar" parent="Widget.MaterialComponents.Toolbar.Primary">
        <item name="android:background">@color/primary_blue</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
        <item name="navigationIconTint">@color/white</item>
    </style>
    
    <!-- Bottom Navigation Styles -->
    <style name="Widget.App.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">@color/surface_light</item>
        <item name="itemIconTint">@color/bottom_nav_color_selector</item>
        <item name="itemTextColor">@color/bottom_nav_color_selector</item>
        <item name="elevation">8dp</item>
        <item name="labelVisibilityMode">labeled</item>
    </style>
    
    <!-- Chip Styles -->
    <style name="Widget.App.Chip" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="chipBackgroundColor">@color/secondary_blue</item>
        <item name="android:textColor">@color/primary_blue</item>
        <item name="chipStrokeColor">@color/primary_blue</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipCornerRadius">16dp</item>
    </style>
    
    <!-- Progress Bar Styles -->
    <style name="Widget.App.ProgressBar" parent="Widget.AppCompat.ProgressBar">
        <item name="android:indeterminateTint">@color/primary_blue</item>
    </style>
    
    <!-- Divider Styles -->
    <style name="Widget.App.Divider">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/divider</item>
    </style>
    
    <!-- Section Header Styles -->
    <style name="TextAppearance.App.SectionHeader" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.02</item>
    </style>
    
    <!-- Book Title Styles -->
    <style name="TextAppearance.App.BookTitle" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
    </style>
    
    <!-- Section Title Styles -->
    <style name="TextAppearance.App.SectionTitle" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:maxLines">3</item>
        <item name="android:ellipsize">end</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>
    
    <!-- Reading Content Styles -->
    <style name="TextAppearance.App.ReadingContent" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:lineSpacingMultiplier">1.5</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">serif</item>
    </style>
    
    <!-- Tag Styles -->
    <style name="Widget.App.Tag" parent="Widget.MaterialComponents.Chip.Entry">
        <item name="chipBackgroundColor">@color/secondary_blue_light</item>
        <item name="android:textColor">@color/primary_blue</item>
        <item name="chipCornerRadius">12dp</item>
        <item name="android:textSize">12sp</item>
    </style>
    
    <!-- Search View Styles -->
    <style name="Widget.App.SearchView" parent="Widget.AppCompat.SearchView">
        <item name="android:background">@color/surface_light</item>
        <item name="queryHint">@string/search_hint</item>
        <item name="iconifiedByDefault">false</item>
    </style>
    
    <!-- Floating Action Button Styles -->
    <style name="Widget.App.FloatingActionButton" parent="Widget.MaterialComponents.FloatingActionButton">
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="tint">@color/white</item>
        <item name="elevation">6dp</item>
        <item name="pressedTranslationZ">12dp</item>
    </style>
    
</resources>
