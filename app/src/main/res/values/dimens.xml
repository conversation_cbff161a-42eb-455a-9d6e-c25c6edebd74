<resources>
    <!-- <PERSON><PERSON> and <PERSON><PERSON> -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_extra_large">32dp</dimen>

    <dimen name="padding_small">8dp</dimen>
    <dimen name="padding_medium">16dp</dimen>
    <dimen name="padding_large">24dp</dimen>
    <dimen name="padding_extra_large">32dp</dimen>

    <!-- Touch Target Sizes (Accessibility) -->
    <dimen name="touch_target_min">48dp</dimen>
    <dimen name="button_height">56dp</dimen>
    <dimen name="fab_size">56dp</dimen>
    <dimen name="toolbar_height">56dp</dimen>

    <!-- Text Sizes -->
    <dimen name="text_size_micro">10sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_medium">14sp</dimen>
    <dimen name="text_size_normal">16sp</dimen>
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_extra_large">20sp</dimen>
    <dimen name="text_size_headline">24sp</dimen>
    <dimen name="text_size_title">28sp</dimen>

    <!-- Card and Container Dimensions -->
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="card_margin">8dp</dimen>

    <!-- Book and Section Item Dimensions -->
    <dimen name="book_item_height">120dp</dimen>
    <dimen name="book_cover_width">80dp</dimen>
    <dimen name="book_cover_height">100dp</dimen>
    <dimen name="section_item_min_height">72dp</dimen>

    <!-- Bottom Navigation -->
    <dimen name="bottom_nav_height">56dp</dimen>
    <dimen name="bottom_nav_elevation">8dp</dimen>

    <!-- Search and Input -->
    <dimen name="search_bar_height">48dp</dimen>
    <dimen name="input_field_height">56dp</dimen>

    <!-- Progress and Loading -->
    <dimen name="progress_bar_size">48dp</dimen>
    <dimen name="progress_bar_stroke_width">4dp</dimen>

    <!-- Dividers and Borders -->
    <dimen name="divider_height">1dp</dimen>
    <dimen name="border_width">1dp</dimen>
    <dimen name="border_width_thick">2dp</dimen>

    <!-- Corner Radius -->
    <dimen name="corner_radius_small">4dp</dimen>
    <dimen name="corner_radius_medium">8dp</dimen>
    <dimen name="corner_radius_large">12dp</dimen>
    <dimen name="corner_radius_extra_large">16dp</dimen>

    <!-- Elevation -->
    <dimen name="elevation_small">2dp</dimen>
    <dimen name="elevation_medium">4dp</dimen>
    <dimen name="elevation_large">8dp</dimen>
    <dimen name="elevation_extra_large">16dp</dimen>

    <!-- Reading Settings -->
    <dimen name="reading_content_margin">20dp</dimen>
    <dimen name="reading_line_spacing">6dp</dimen>

    <!-- Profile and Avatar -->
    <dimen name="avatar_size_small">32dp</dimen>
    <dimen name="avatar_size_medium">48dp</dimen>
    <dimen name="avatar_size_large">64dp</dimen>
    <dimen name="avatar_size_extra_large">96dp</dimen>

    <!-- Tag and Chip Dimensions -->
    <dimen name="chip_height">32dp</dimen>
    <dimen name="chip_corner_radius">16dp</dimen>
    <dimen name="tag_corner_radius">12dp</dimen>

    <!-- Notification and Badge -->
    <dimen name="notification_badge_size">20dp</dimen>
    <dimen name="notification_item_height">80dp</dimen>

    <!-- Shimmer and Loading -->
    <dimen name="shimmer_corner_radius">8dp</dimen>
    <dimen name="shimmer_height_small">16dp</dimen>
    <dimen name="shimmer_height_medium">20dp</dimen>
    <dimen name="shimmer_height_large">24dp</dimen>
</resources>