<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- App Primary Colors -->
    <color name="primary_blue">#4294ff</color>
    <color name="secondary_blue">#e6ecff</color>

    <!-- Primary Color Variants -->
    <color name="primary_blue_dark">#2c6ed6</color>
    <color name="primary_blue_light">#6ba8ff</color>

    <!-- Secondary Color Variants -->
    <color name="secondary_blue_dark">#d1dcf7</color>
    <color name="secondary_blue_light">#f5f7ff</color>

    <!-- Text Colors -->
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_hint">#BDBDBD</color>
    <color name="text_white">#FFFFFF</color>

    <!-- Background Colors -->
    <color name="background_light">#FFFFFF</color>
    <color name="background_dark">#121212</color>
    <color name="surface_light">#FAFAFA</color>
    <color name="surface_dark">#1E1E1E</color>

    <!-- Status Colors -->
    <color name="success">#4CAF50</color>
    <color name="error">#F44336</color>
    <color name="warning">#FF9800</color>
    <color name="info">#2196F3</color>

    <!-- Divider and Border Colors -->
    <color name="divider">#E0E0E0</color>
    <color name="border">#CCCCCC</color>

    <!-- Standard Colors -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="transparent">#00000000</color>

    <!-- Shimmer Colors -->
    <color name="shimmer_base">#F0F0F0</color>
    <color name="shimmer_highlight">#FFFFFF</color>

    <!-- Reading Settings Colors -->
    <color name="reading_background_sepia">#F4F1E8</color>
    <color name="reading_background_dark">#1A1A1A</color>
    <color name="reading_text_sepia">#5D4E37</color>
</resources>