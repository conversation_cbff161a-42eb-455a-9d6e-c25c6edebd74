<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Dark theme for the application -->
    <style name="Theme.Indian_law_library" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue_light</item>
        <item name="colorPrimaryVariant">@color/primary_blue</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary_blue_light</item>
        <item name="colorSecondaryVariant">@color/secondary_blue</item>
        <item name="colorOnSecondary">@color/text_primary</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="colorSurface">@color/surface_dark</item>
        <!-- Text colors for dark theme -->
        <item name="android:textColorPrimary">@color/text_white</item>
        <item name="android:textColorSecondary">@color/text_hint</item>
    </style>

    <!-- Dark theme without action bar for authentication screens -->
    <style name="Theme.Indian_law_library.NoActionBar" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue_light</item>
        <item name="colorPrimaryVariant">@color/primary_blue</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary_blue_light</item>
        <item name="colorSecondaryVariant">@color/secondary_blue</item>
        <item name="colorOnSecondary">@color/text_primary</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/primary_blue</item>
        <item name="android:windowLightStatusBar">false</item>
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="colorSurface">@color/surface_dark</item>
        <!-- Text colors for dark theme -->
        <item name="android:textColorPrimary">@color/text_white</item>
        <item name="android:textColorSecondary">@color/text_hint</item>
    </style>
</resources>