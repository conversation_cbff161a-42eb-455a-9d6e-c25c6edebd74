<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light"
    tools:context=".ui.auth.ForgotPasswordActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        app:title="Reset Password"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp">

            <!-- Form Layout -->
            <LinearLayout
                android:id="@+id/layout_form"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <!-- Icon -->
                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginTop="48dp"
                    android:src="@drawable/ic_lock_reset"
                    android:contentDescription="Reset Password"
                    app:tint="@color/primary_blue" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="Forgot Password?"
                    android:textColor="@color/text_primary"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginHorizontal="16dp"
                    android:text="Enter your email address and we'll send you a link to reset your password."
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:lineSpacingExtra="4dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:hint="@string/email"
                    app:boxStrokeColor="@color/primary_blue"
                    app:hintTextColor="@color/primary_blue">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_send_reset_email"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="24dp"
                    android:text="Send Reset Email"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/primary_blue"
                    app:cornerRadius="8dp" />

                <TextView
                    android:id="@+id/tv_back_to_login"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="Back to Login"
                    android:textColor="@color/primary_blue"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:background="?attr/selectableItemBackground"
                    android:padding="12dp" />

            </LinearLayout>

            <!-- Success Layout -->
            <LinearLayout
                android:id="@+id/layout_success"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <!-- Success Icon -->
                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginTop="48dp"
                    android:src="@drawable/ic_email_sent"
                    android:contentDescription="Email Sent"
                    app:tint="@color/success" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="Email Sent!"
                    android:textColor="@color/text_primary"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginHorizontal="16dp"
                    android:text="We've sent a password reset link to your email address. Please check your inbox and follow the instructions."
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:lineSpacingExtra="4dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_back_to_login"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="32dp"
                    android:text="Back to Login"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/primary_blue"
                    app:cornerRadius="8dp" />

            </LinearLayout>

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:indeterminateTint="@color/primary_blue"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</LinearLayout>
