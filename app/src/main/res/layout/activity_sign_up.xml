<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/background_light"
    tools:context=".ui.auth.SignUpActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            app:title="@string/signup"
            app:titleTextColor="@color/white"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:navigationIconTint="@color/white" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp">

            <!-- App Logo and Title -->
            <ImageView
                android:id="@+id/iv_logo"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginTop="24dp"
                android:src="@drawable/ic_launcher_foreground"
                android:contentDescription="@string/app_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Create Account"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_logo" />

            <TextView
                android:id="@+id/tv_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Join the Indian Law Library community"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:gravity="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title" />

            <!-- Sign Up Form -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:hint="@string/name"
                app:boxStrokeColor="@color/primary_blue"
                app:hintTextColor="@color/primary_blue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_subtitle">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_email"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="@string/email"
                app:boxStrokeColor="@color/primary_blue"
                app:hintTextColor="@color/primary_blue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_name">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textEmailAddress"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_password"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="@string/password"
                app:boxStrokeColor="@color/primary_blue"
                app:hintTextColor="@color/primary_blue"
                app:passwordToggleEnabled="true"
                app:passwordToggleTint="@color/primary_blue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_email">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_confirm_password"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="@string/confirm_password"
                app:boxStrokeColor="@color/primary_blue"
                app:hintTextColor="@color/primary_blue"
                app:passwordToggleEnabled="true"
                app:passwordToggleTint="@color/primary_blue"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_password">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_confirm_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_sign_up"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginTop="24dp"
                android:text="@string/signup"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                app:backgroundTint="@color/primary_blue"
                app:cornerRadius="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_confirm_password" />

            <!-- Divider -->
            <View
                android:id="@+id/divider_left"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="16dp"
                android:background="@color/divider"
                app:layout_constraintEnd_toStartOf="@id/tv_or"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btn_sign_up" />

            <TextView
                android:id="@+id/tv_or"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="OR"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/divider_left"
                app:layout_constraintBottom_toBottomOf="@id/divider_left" />

            <View
                android:id="@+id/divider_right"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginStart="16dp"
                android:background="@color/divider"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_or"
                app:layout_constraintTop_toTopOf="@id/divider_left" />

            <!-- Google Sign Up Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_google_sign_up"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginTop="24dp"
                android:text="@string/signup_with_google"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                app:backgroundTint="@color/white"
                app:strokeColor="@color/border"
                app:strokeWidth="1dp"
                app:cornerRadius="8dp"
                app:icon="@drawable/ic_google"
                app:iconGravity="textStart"
                app:iconPadding="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/divider_left" />

            <!-- Login Link -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="24dp"
                android:orientation="horizontal"
                android:gravity="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btn_google_sign_up"
                app:layout_constraintBottom_toBottomOf="parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Already have an account? "
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_login"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login"
                    android:textColor="@color/primary_blue"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:background="?attr/selectableItemBackground"
                    android:padding="4dp" />

            </LinearLayout>

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:indeterminateTint="@color/primary_blue"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

</ScrollView>
