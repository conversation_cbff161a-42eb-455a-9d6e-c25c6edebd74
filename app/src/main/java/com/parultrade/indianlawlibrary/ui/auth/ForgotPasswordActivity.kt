package com.parultrade.indianlawlibrary.ui.auth

import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.parultrade.indianlawlibrary.databinding.ActivityForgotPasswordBinding
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ForgotPasswordActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityForgotPasswordBinding
    private val viewModel: AuthViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityForgotPasswordBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
    }
    
    private fun setupUI() {
        binding.apply {
            btnSendResetEmail.setOnClickListener {
                val email = etEmail.text.toString().trim()
                viewModel.sendPasswordResetEmail(email)
            }
            
            toolbar.setNavigationOnClickListener {
                finish()
            }
            
            tvBackToLogin.setOnClickListener {
                finish()
            }
        }
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                binding.apply {
                    progressBar.isVisible = state.isLoading
                    btnSendResetEmail.isEnabled = !state.isLoading
                    
                    state.errorMessage?.let { message ->
                        Toast.makeText(this@ForgotPasswordActivity, message, Toast.LENGTH_LONG).show()
                        viewModel.clearMessages()
                    }
                    
                    state.successMessage?.let { message ->
                        Toast.makeText(this@ForgotPasswordActivity, message, Toast.LENGTH_LONG).show()
                        viewModel.clearMessages()
                        
                        // Show success state
                        layoutSuccess.isVisible = true
                        layoutForm.isVisible = false
                    }
                }
            }
        }
    }
}
