package com.parultrade.indianlawlibrary.ui.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.firebase.auth.FirebaseUser
import com.parultrade.indianlawlibrary.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AuthUiState())
    val uiState: StateFlow<AuthUiState> = _uiState.asStateFlow()
    
    private val _authResult = MutableStateFlow<AuthResult?>(null)
    val authResult: StateFlow<AuthResult?> = _authResult.asStateFlow()
    
    init {
        // Check if user is already logged in
        checkAuthState()
    }
    
    private fun checkAuthState() {
        val currentUser = authRepository.getCurrentUser()
        if (currentUser != null) {
            _authResult.value = AuthResult.Success(currentUser)
        }
    }
    
    fun signUpWithEmail(email: String, password: String, confirmPassword: String, name: String) {
        if (!validateSignUpInput(email, password, confirmPassword, name)) {
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
            
            val result = authRepository.signUpWithEmail(email, password, name)
            
            if (result.isSuccess) {
                _authResult.value = AuthResult.Success(result.getOrNull()!!)
                _uiState.value = _uiState.value.copy(isLoading = false)
            } else {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = result.exceptionOrNull()?.message ?: "Sign up failed"
                )
            }
        }
    }
    
    fun signInWithEmail(email: String, password: String) {
        if (!validateSignInInput(email, password)) {
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
            
            val result = authRepository.signInWithEmail(email, password)
            
            if (result.isSuccess) {
                _authResult.value = AuthResult.Success(result.getOrNull()!!)
                _uiState.value = _uiState.value.copy(isLoading = false)
            } else {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = result.exceptionOrNull()?.message ?: "Sign in failed"
                )
            }
        }
    }
    
    fun signInWithGoogle(account: GoogleSignInAccount) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
            
            val result = authRepository.signInWithGoogle(account)
            
            if (result.isSuccess) {
                _authResult.value = AuthResult.Success(result.getOrNull()!!)
                _uiState.value = _uiState.value.copy(isLoading = false)
            } else {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = result.exceptionOrNull()?.message ?: "Google sign in failed"
                )
            }
        }
    }
    
    fun sendPasswordResetEmail(email: String) {
        if (email.isBlank()) {
            _uiState.value = _uiState.value.copy(errorMessage = "Please enter your email address")
            return
        }
        
        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            _uiState.value = _uiState.value.copy(errorMessage = "Please enter a valid email address")
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
            
            val result = authRepository.sendPasswordResetEmail(email)
            
            if (result.isSuccess) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    successMessage = "Password reset email sent. Please check your inbox."
                )
            } else {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = result.exceptionOrNull()?.message ?: "Failed to send password reset email"
                )
            }
        }
    }
    
    fun signOut() {
        authRepository.signOut()
        _authResult.value = AuthResult.SignedOut
    }
    
    private fun validateSignUpInput(email: String, password: String, confirmPassword: String, name: String): Boolean {
        when {
            name.isBlank() -> {
                _uiState.value = _uiState.value.copy(errorMessage = "Please enter your name")
                return false
            }
            email.isBlank() -> {
                _uiState.value = _uiState.value.copy(errorMessage = "Please enter your email address")
                return false
            }
            !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() -> {
                _uiState.value = _uiState.value.copy(errorMessage = "Please enter a valid email address")
                return false
            }
            password.isBlank() -> {
                _uiState.value = _uiState.value.copy(errorMessage = "Please enter a password")
                return false
            }
            password.length < 6 -> {
                _uiState.value = _uiState.value.copy(errorMessage = "Password must be at least 6 characters")
                return false
            }
            password != confirmPassword -> {
                _uiState.value = _uiState.value.copy(errorMessage = "Passwords do not match")
                return false
            }
            else -> return true
        }
    }
    
    private fun validateSignInInput(email: String, password: String): Boolean {
        when {
            email.isBlank() -> {
                _uiState.value = _uiState.value.copy(errorMessage = "Please enter your email address")
                return false
            }
            !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() -> {
                _uiState.value = _uiState.value.copy(errorMessage = "Please enter a valid email address")
                return false
            }
            password.isBlank() -> {
                _uiState.value = _uiState.value.copy(errorMessage = "Please enter your password")
                return false
            }
            else -> return true
        }
    }
    
    fun clearMessages() {
        _uiState.value = _uiState.value.copy(errorMessage = null, successMessage = null)
    }
    
    fun clearAuthResult() {
        _authResult.value = null
    }
}

data class AuthUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null
)

sealed class AuthResult {
    data class Success(val user: FirebaseUser) : AuthResult()
    object SignedOut : AuthResult()
}
