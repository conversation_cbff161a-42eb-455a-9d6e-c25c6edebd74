package com.parultrade.indianlawlibrary.data.model

import com.google.firebase.Timestamp
import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp

/**
 * Data class representing a section within a legal book
 */
data class Section(
    @DocumentId
    val sectionId: String = "",
    val bookId: String = "",
    val title: String = "",
    val content: String = "",
    val orderInBook: Int = 0,
    val references: List<String> = emptyList(), // List of section IDs that this section references
    val amendment: String = "", // Amendment information if any
    @ServerTimestamp
    val createdAt: Timestamp? = null,
    @ServerTimestamp
    val updatedAt: Timestamp? = null,
    
    // Additional fields for UI purposes (not stored in Firestore)
    val isFavorited: Boolean = false,
    val tags: List<String> = emptyList(),
    val bookTitle: String = "" // For display purposes in favorites
) {
    // No-argument constructor for Firestore
    constructor() : this(
        sectionId = "",
        bookId = "",
        title = "",
        content = "",
        orderInBook = 0,
        references = emptyList(),
        amendment = "",
        createdAt = null,
        updatedAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "bookId" to bookId,
            "title" to title,
            "content" to content,
            "orderInBook" to orderInBook,
            "references" to references,
            "amendment" to amendment,
            "createdAt" to createdAt,
            "updatedAt" to updatedAt
        )
    }
    
    /**
     * Get formatted section number for display
     */
    fun getFormattedSectionNumber(): String {
        return "धारा $orderInBook"
    }
    
    /**
     * Check if section has amendments
     */
    fun hasAmendment(): Boolean {
        return amendment.isNotBlank()
    }
    
    /**
     * Check if section has cross-references
     */
    fun hasReferences(): Boolean {
        return references.isNotEmpty()
    }
}

/**
 * Data class for section search results
 */
data class SectionSearchResult(
    val section: Section,
    val matchType: SearchMatchType,
    val highlightedText: String = ""
)

/**
 * Enum for different types of search matches
 */
enum class SearchMatchType {
    TITLE_MATCH,
    CONTENT_MATCH,
    BOOK_TITLE_MATCH
}
