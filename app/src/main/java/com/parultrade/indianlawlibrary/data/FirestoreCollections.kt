package com.parultrade.indianlawlibrary.data

/**
 * Constants for Firestore collection names
 * This ensures consistency across the app and makes it easy to change collection names if needed
 */
object FirestoreCollections {
    
    // Main collections
    const val BOOKS = "books"
    const val SECTIONS = "sections"
    const val USERS = "users"
    const val NOTIFICATIONS = "notifications"
    const val SUBSCRIPTIONS = "subscriptions"
    
    // User-specific collections
    const val USER_SETTINGS = "user_settings"
    const val USER_BOOKS = "user_books"
    const val USER_FAVORITES = "user_favorites"
    const val USER_TAGS = "user_tags"
    const val USER_TAG_SECTIONS = "user_tag_sections"
    const val USER_SEARCHES = "user_searches"
    const val USER_NOTIFICATIONS = "user_notifications"
    const val USER_NOTIFICATION_PREFERENCES = "user_notification_preferences"
    
    // Admin collections
    const val ADMIN_USERS = "admin_users"
    const val APP_CONFIG = "app_config"
    const val ANALYTICS = "analytics"
}

/**
 * Firestore field names for consistent querying
 */
object FirestoreFields {
    
    // Common fields
    const val ID = "id"
    const val CREATED_AT = "createdAt"
    const val UPDATED_AT = "updatedAt"
    const val USER_ID = "userId"
    
    // Book fields
    const val BOOK_ID = "bookId"
    const val TITLE = "title"
    const val TOTAL_SECTIONS = "totalSections"
    const val CATEGORY = "category"
    const val PUBLISHED_YEAR = "publishedYear"
    const val JURISDICTION = "jurisdiction"
    const val IS_NEW = "isNew"
    
    // Section fields
    const val SECTION_ID = "sectionId"
    const val CONTENT = "content"
    const val ORDER_IN_BOOK = "orderInBook"
    const val REFERENCES = "references"
    const val AMENDMENT = "amendment"
    
    // User fields
    const val NAME = "name"
    const val EMAIL = "email"
    const val MOBILE_NUMBER = "mobileNumber"
    const val ADDRESS = "address"
    const val PROFILE_PHOTO_URL = "profilePhotoUrl"
    
    // User interaction fields
    const val IS_PINNED = "isPinned"
    const val READING_PROGRESS = "readingProgress"
    const val CURRENT_SECTION_ID = "currentSectionId"
    const val LAST_ACCESSED_AT = "lastAccessedAt"
    
    // Tag fields
    const val TAG_ID = "tagId"
    const val COLOR = "color"
    
    // Notification fields
    const val NOTIFICATION_ID = "notificationId"
    const val MESSAGE = "message"
    const val TYPE = "type"
    const val IS_GLOBAL = "isGlobal"
    const val TARGET_USER_IDS = "targetUserIds"
    const val IS_READ = "isRead"
    const val IS_CLICKED = "isClicked"
    const val READ_AT = "readAt"
    const val CLICKED_AT = "clickedAt"
    const val RECEIVED_AT = "receivedAt"
    const val SCHEDULED_AT = "scheduledAt"
    const val EXPIRES_AT = "expiresAt"
    
    // Search fields
    const val QUERY = "query"
    const val RESULT_COUNT = "resultCount"
    const val SEARCHED_AT = "searchedAt"
    
    // Settings fields
    const val THEME_MODE = "themeMode"
    const val FONT_SIZE = "fontSize"
    const val TEXT_COLOR = "textColor"
    const val BACKGROUND_COLOR = "backgroundColor"
    const val BRIGHTNESS = "brightness"
    const val TEXT_ALIGNMENT = "textAlignment"
    const val LINE_SPACING = "lineSpacing"
    
    // Notification preferences
    const val NEW_BOOKS_ENABLED = "newBooksEnabled"
    const val AMENDMENTS_ENABLED = "amendmentsEnabled"
    const val GENERAL_UPDATES_ENABLED = "generalUpdatesEnabled"
    const val PUSH_NOTIFICATIONS_ENABLED = "pushNotificationsEnabled"
    const val EMAIL_NOTIFICATIONS_ENABLED = "emailNotificationsEnabled"
    
    // Subscription fields
    const val SUBSCRIPTION_ID = "subscriptionId"
    const val SUBSCRIPTION_TYPE = "type"
    const val START_DATE = "startDate"
    const val END_DATE = "endDate"
    const val IS_ACTIVE = "isActive"
    const val FEATURES = "features"
}

/**
 * Firestore query limits
 */
object FirestoreQueryLimits {
    const val DEFAULT_PAGE_SIZE = 20
    const val BOOKS_PAGE_SIZE = 15
    const val SECTIONS_PAGE_SIZE = 25
    const val NOTIFICATIONS_PAGE_SIZE = 20
    const val SEARCH_RESULTS_LIMIT = 50
    const val RECENT_BOOKS_LIMIT = 10
    const val LATEST_BOOKS_LIMIT = 5
    const val PINNED_BOOKS_LIMIT = 10
    const val FAVORITES_PAGE_SIZE = 20
    const val TAGS_LIMIT = 100
    const val SEARCH_HISTORY_LIMIT = 20
}

/**
 * Firestore indexes that should be created in the Firebase console
 */
object FirestoreIndexes {
    const val BOOKS_CATEGORY_TITLE = "books: category (Ascending), title (Ascending)"
    const val BOOKS_IS_NEW_CREATED_AT = "books: isNew (Ascending), createdAt (Descending)"
    const val SECTIONS_BOOK_ID_ORDER = "sections: bookId (Ascending), orderInBook (Ascending)"
    const val USER_BOOKS_USER_ID_PINNED = "user_books: userId (Ascending), isPinned (Ascending)"
    const val USER_BOOKS_USER_ID_LAST_ACCESSED = "user_books: userId (Ascending), lastAccessedAt (Descending)"
    const val USER_FAVORITES_USER_ID_CREATED_AT = "user_favorites: userId (Ascending), createdAt (Descending)"
    const val USER_TAGS_USER_ID_NAME = "user_tags: userId (Ascending), name (Ascending)"
    const val USER_TAG_SECTIONS_TAG_ID_CREATED_AT = "user_tag_sections: tagId (Ascending), createdAt (Descending)"
    const val USER_NOTIFICATIONS_USER_ID_NOTIFICATION_ID = "user_notifications: userId (Ascending), notificationId (Ascending)"
    const val NOTIFICATIONS_IS_GLOBAL_CREATED_AT = "notifications: isGlobal (Ascending), createdAt (Descending)"
    const val NOTIFICATIONS_TARGET_USERS_CREATED_AT = "notifications: targetUserIds (Array), createdAt (Descending)"
}

/**
 * Default values for Firestore documents
 */
object FirestoreDefaults {
    const val DEFAULT_READING_PROGRESS = 0f
    const val DEFAULT_FONT_SIZE = 16f
    const val DEFAULT_BRIGHTNESS = 0.5f
    const val DEFAULT_LINE_SPACING = 1.5f
    const val DEFAULT_TAG_COLOR = "#4294ff"
    const val DEFAULT_THEME_MODE = "SYSTEM"
    const val DEFAULT_TEXT_ALIGNMENT = "LEFT"
    const val DEFAULT_TEXT_COLOR = "#212121"
    const val DEFAULT_BACKGROUND_COLOR = "#FFFFFF"
}
