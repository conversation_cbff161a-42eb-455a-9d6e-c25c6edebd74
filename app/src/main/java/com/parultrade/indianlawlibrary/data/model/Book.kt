package com.parultrade.indianlawlibrary.data.model

import com.google.firebase.Timestamp
import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp

/**
 * Data class representing a legal book in the Indian Law Library
 */
data class Book(
    @DocumentId
    val bookId: String = "",
    val title: String = "",
    val totalSections: Int = 0,
    val category: String = "",
    val publishedYear: String = "",
    val jurisdiction: String = "",
    val isNew: Boolean = false,
    @ServerTimestamp
    val createdAt: Timestamp? = null,
    @ServerTimestamp
    val updatedAt: Timestamp? = null,
    
    // Additional fields for UI purposes (not stored in Firestore)
    val isPinned: Boolean = false,
    val readingProgress: Float = 0f,
    val lastAccessedAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        bookId = "",
        title = "",
        totalSections = 0,
        category = "",
        publishedYear = "",
        jurisdiction = "",
        isNew = false,
        createdAt = null,
        updatedAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "title" to title,
            "totalSections" to totalSections,
            "category" to category,
            "publishedYear" to publishedYear,
            "jurisdiction" to jurisdiction,
            "isNew" to isNew,
            "createdAt" to createdAt,
            "updatedAt" to updatedAt
        )
    }
}

/**
 * Book categories enum
 */
enum class BookCategory(val displayName: String) {
    CONSTITUTION("संविधान"),
    CRIMINAL_LAW("आपराधिक कानून"),
    CIVIL_LAW("नागरिक कानून"),
    COMMERCIAL_LAW("वाणिज्यिक कानून"),
    LABOR_LAW("श्रम कानून"),
    TAX_LAW("कर कानून"),
    FAMILY_LAW("पारिवारिक कानून"),
    PROPERTY_LAW("संपत्ति कानून"),
    ADMINISTRATIVE_LAW("प्रशासनिक कानून"),
    ENVIRONMENTAL_LAW("पर्यावरण कानून"),
    OTHER("अन्य")
}

/**
 * Jurisdiction enum
 */
enum class Jurisdiction(val displayName: String) {
    CENTRAL("केंद्रीय"),
    STATE("राज्य"),
    LOCAL("स्थानीय"),
    SUPREME_COURT("सर्वोच्च न्यायालय"),
    HIGH_COURT("उच्च न्यायालय"),
    DISTRICT_COURT("जिला न्यायालय")
}
