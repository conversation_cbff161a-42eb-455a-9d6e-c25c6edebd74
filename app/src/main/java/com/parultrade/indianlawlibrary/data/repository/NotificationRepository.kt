package com.parultrade.indianlawlibrary.data.repository

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.parultrade.indianlawlibrary.data.model.Notification
import com.parultrade.indianlawlibrary.data.model.UserNotification
import com.parultrade.indianlawlibrary.data.model.UserNotificationPreferences
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    companion object {
        private const val NOTIFICATIONS_COLLECTION = "notifications"
        private const val USER_NOTIFICATIONS_COLLECTION = "user_notifications"
        private const val USER_NOTIFICATION_PREFERENCES_COLLECTION = "user_notification_preferences"
    }
    
    /**
     * Get notifications for user
     */
    suspend fun getNotificationsForUser(userId: String, limit: Int = 20): Result<List<Pair<Notification, UserNotification?>>> {
        return try {
            // Get global notifications
            val globalNotifications = firestore.collection(NOTIFICATIONS_COLLECTION)
                .whereEqualTo("isGlobal", true)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()
            
            // Get user-specific notifications
            val userSpecificNotifications = firestore.collection(NOTIFICATIONS_COLLECTION)
                .whereEqualTo("isGlobal", false)
                .whereArrayContains("targetUserIds", userId)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()
            
            // Combine and get user notification status
            val allNotifications = (globalNotifications.documents + userSpecificNotifications.documents)
                .mapNotNull { doc ->
                    doc.toObject(Notification::class.java)?.copy(notificationId = doc.id)
                }
                .sortedByDescending { it.createdAt }
                .take(limit)
            
            // Get user notification statuses
            val notificationIds = allNotifications.map { it.notificationId }
            val userNotificationStatuses = if (notificationIds.isNotEmpty()) {
                firestore.collection(USER_NOTIFICATIONS_COLLECTION)
                    .whereEqualTo("userId", userId)
                    .whereIn("notificationId", notificationIds)
                    .get()
                    .await()
                    .documents
                    .mapNotNull { doc ->
                        doc.toObject(UserNotification::class.java)?.copy(id = doc.id)
                    }
                    .associateBy { it.notificationId }
            } else {
                emptyMap()
            }
            
            val result = allNotifications.map { notification ->
                notification to userNotificationStatuses[notification.notificationId]
            }
            
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Mark notification as read
     */
    suspend fun markNotificationAsRead(userId: String, notificationId: String): Result<Unit> {
        return try {
            val existingDoc = firestore.collection(USER_NOTIFICATIONS_COLLECTION)
                .whereEqualTo("userId", userId)
                .whereEqualTo("notificationId", notificationId)
                .get()
                .await()
            
            if (existingDoc.documents.isNotEmpty()) {
                // Update existing record
                val doc = existingDoc.documents.first()
                doc.reference.update(
                    mapOf(
                        "isRead" to true,
                        "readAt" to com.google.firebase.Timestamp.now()
                    )
                ).await()
            } else {
                // Create new record
                val userNotification = UserNotification(
                    userId = userId,
                    notificationId = notificationId,
                    isRead = true
                )
                firestore.collection(USER_NOTIFICATIONS_COLLECTION)
                    .add(userNotification.toMap())
                    .await()
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Mark notification as clicked
     */
    suspend fun markNotificationAsClicked(userId: String, notificationId: String): Result<Unit> {
        return try {
            val existingDoc = firestore.collection(USER_NOTIFICATIONS_COLLECTION)
                .whereEqualTo("userId", userId)
                .whereEqualTo("notificationId", notificationId)
                .get()
                .await()
            
            if (existingDoc.documents.isNotEmpty()) {
                // Update existing record
                val doc = existingDoc.documents.first()
                doc.reference.update(
                    mapOf(
                        "isClicked" to true,
                        "clickedAt" to com.google.firebase.Timestamp.now()
                    )
                ).await()
            } else {
                // Create new record
                val userNotification = UserNotification(
                    userId = userId,
                    notificationId = notificationId,
                    isClicked = true
                )
                firestore.collection(USER_NOTIFICATIONS_COLLECTION)
                    .add(userNotification.toMap())
                    .await()
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get unread notification count
     */
    suspend fun getUnreadNotificationCount(userId: String): Result<Int> {
        return try {
            // Get all notifications for user
            val allNotifications = getNotificationsForUser(userId, 100)
            
            if (allNotifications.isSuccess) {
                val unreadCount = allNotifications.getOrNull()?.count { (_, userNotification) ->
                    userNotification?.isRead != true
                } ?: 0
                
                Result.success(unreadCount)
            } else {
                Result.failure(allNotifications.exceptionOrNull() ?: Exception("Failed to get notifications"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get user notification preferences
     */
    suspend fun getUserNotificationPreferences(userId: String): Result<UserNotificationPreferences?> {
        return try {
            val snapshot = firestore.collection(USER_NOTIFICATION_PREFERENCES_COLLECTION)
                .document(userId)
                .get()
                .await()
            
            val preferences = snapshot.toObject(UserNotificationPreferences::class.java)?.copy(userId = snapshot.id)
            Result.success(preferences)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Save user notification preferences
     */
    suspend fun saveUserNotificationPreferences(preferences: UserNotificationPreferences): Result<Unit> {
        return try {
            firestore.collection(USER_NOTIFICATION_PREFERENCES_COLLECTION)
                .document(preferences.userId)
                .set(preferences.toMap())
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Create notification (Admin function)
     */
    suspend fun createNotification(notification: Notification): Result<String> {
        return try {
            val docRef = firestore.collection(NOTIFICATIONS_COLLECTION)
                .add(notification.toMap())
                .await()
            Result.success(docRef.id)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Delete notification (Admin function)
     */
    suspend fun deleteNotification(notificationId: String): Result<Unit> {
        return try {
            firestore.collection(NOTIFICATIONS_COLLECTION)
                .document(notificationId)
                .delete()
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Clear all notifications for user
     */
    suspend fun clearAllNotifications(userId: String): Result<Unit> {
        return try {
            val userNotifications = firestore.collection(USER_NOTIFICATIONS_COLLECTION)
                .whereEqualTo("userId", userId)
                .get()
                .await()
            
            userNotifications.documents.forEach { doc ->
                doc.reference.delete().await()
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
