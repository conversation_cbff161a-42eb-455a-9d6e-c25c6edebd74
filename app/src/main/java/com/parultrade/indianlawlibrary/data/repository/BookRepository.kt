package com.parultrade.indianlawlibrary.data.repository

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.parultrade.indianlawlibrary.data.model.Book
import com.parultrade.indianlawlibrary.data.model.Section
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BookRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    companion object {
        private const val BOOKS_COLLECTION = "books"
        private const val SECTIONS_COLLECTION = "sections"
    }
    
    /**
     * Get all books
     */
    suspend fun getAllBooks(): Result<List<Book>> {
        return try {
            val snapshot = firestore.collection(BOOKS_COLLECTION)
                .orderBy("title")
                .get()
                .await()
            
            val books = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Book::class.java)?.copy(bookId = doc.id)
            }
            Result.success(books)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get latest books (newly added)
     */
    suspend fun getLatestBooks(limit: Int = 5): Result<List<Book>> {
        return try {
            val snapshot = firestore.collection(BOOKS_COLLECTION)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()
            
            val books = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Book::class.java)?.copy(bookId = doc.id)
            }
            Result.success(books)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get new books (marked as new)
     */
    suspend fun getNewBooks(): Result<List<Book>> {
        return try {
            val snapshot = firestore.collection(BOOKS_COLLECTION)
                .whereEqualTo("isNew", true)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()
            
            val books = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Book::class.java)?.copy(bookId = doc.id)
            }
            Result.success(books)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get book by ID
     */
    suspend fun getBookById(bookId: String): Result<Book?> {
        return try {
            val snapshot = firestore.collection(BOOKS_COLLECTION)
                .document(bookId)
                .get()
                .await()
            
            val book = snapshot.toObject(Book::class.java)?.copy(bookId = snapshot.id)
            Result.success(book)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Search books by title
     */
    suspend fun searchBooks(query: String): Result<List<Book>> {
        return try {
            val snapshot = firestore.collection(BOOKS_COLLECTION)
                .orderBy("title")
                .startAt(query)
                .endAt(query + "\uf8ff")
                .get()
                .await()
            
            val books = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Book::class.java)?.copy(bookId = doc.id)
            }
            Result.success(books)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get books by category
     */
    suspend fun getBooksByCategory(category: String): Result<List<Book>> {
        return try {
            val snapshot = firestore.collection(BOOKS_COLLECTION)
                .whereEqualTo("category", category)
                .orderBy("title")
                .get()
                .await()
            
            val books = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Book::class.java)?.copy(bookId = doc.id)
            }
            Result.success(books)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get sections for a book
     */
    suspend fun getSectionsForBook(bookId: String): Result<List<Section>> {
        return try {
            val snapshot = firestore.collection(SECTIONS_COLLECTION)
                .whereEqualTo("bookId", bookId)
                .orderBy("orderInBook")
                .get()
                .await()
            
            val sections = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Section::class.java)?.copy(sectionId = doc.id)
            }
            Result.success(sections)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get section by ID
     */
    suspend fun getSectionById(sectionId: String): Result<Section?> {
        return try {
            val snapshot = firestore.collection(SECTIONS_COLLECTION)
                .document(sectionId)
                .get()
                .await()
            
            val section = snapshot.toObject(Section::class.java)?.copy(sectionId = snapshot.id)
            Result.success(section)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Search sections by title and content
     */
    suspend fun searchSections(query: String): Result<List<Section>> {
        return try {
            // Note: Firestore doesn't support full-text search natively
            // This is a basic implementation. For production, consider using Algolia or similar
            val titleSnapshot = firestore.collection(SECTIONS_COLLECTION)
                .orderBy("title")
                .startAt(query)
                .endAt(query + "\uf8ff")
                .get()
                .await()
            
            val sections = titleSnapshot.documents.mapNotNull { doc ->
                doc.toObject(Section::class.java)?.copy(sectionId = doc.id)
            }
            Result.success(sections)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get next section in a book
     */
    suspend fun getNextSection(bookId: String, currentOrder: Int): Result<Section?> {
        return try {
            val snapshot = firestore.collection(SECTIONS_COLLECTION)
                .whereEqualTo("bookId", bookId)
                .whereGreaterThan("orderInBook", currentOrder)
                .orderBy("orderInBook")
                .limit(1)
                .get()
                .await()
            
            val section = snapshot.documents.firstOrNull()?.let { doc ->
                doc.toObject(Section::class.java)?.copy(sectionId = doc.id)
            }
            Result.success(section)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get previous section in a book
     */
    suspend fun getPreviousSection(bookId: String, currentOrder: Int): Result<Section?> {
        return try {
            val snapshot = firestore.collection(SECTIONS_COLLECTION)
                .whereEqualTo("bookId", bookId)
                .whereLessThan("orderInBook", currentOrder)
                .orderBy("orderInBook", Query.Direction.DESCENDING)
                .limit(1)
                .get()
                .await()
            
            val section = snapshot.documents.firstOrNull()?.let { doc ->
                doc.toObject(Section::class.java)?.copy(sectionId = doc.id)
            }
            Result.success(section)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
