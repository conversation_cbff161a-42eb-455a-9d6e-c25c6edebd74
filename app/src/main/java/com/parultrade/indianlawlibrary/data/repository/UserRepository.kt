package com.parultrade.indianlawlibrary.data.repository

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.parultrade.indianlawlibrary.data.model.*
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val auth: FirebaseAuth
) {
    companion object {
        private const val USERS_COLLECTION = "users"
        private const val USER_SETTINGS_COLLECTION = "user_settings"
        private const val USER_BOOKS_COLLECTION = "user_books"
        private const val USER_FAVORITES_COLLECTION = "user_favorites"
        private const val USER_TAGS_COLLECTION = "user_tags"
        private const val USER_TAG_SECTIONS_COLLECTION = "user_tag_sections"
        private const val USER_SEARCHES_COLLECTION = "user_searches"
        private const val USER_NOTIFICATIONS_COLLECTION = "user_notifications"
        private const val USER_NOTIFICATION_PREFERENCES_COLLECTION = "user_notification_preferences"
        private const val SUBSCRIPTIONS_COLLECTION = "subscriptions"
    }
    
    /**
     * Get current user ID
     */
    fun getCurrentUserId(): String? = auth.currentUser?.uid
    
    /**
     * Create or update user profile
     */
    suspend fun saveUserProfile(user: User): Result<Unit> {
        return try {
            firestore.collection(USERS_COLLECTION)
                .document(user.userId)
                .set(user.toMap())
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get user profile
     */
    suspend fun getUserProfile(userId: String): Result<User?> {
        return try {
            val snapshot = firestore.collection(USERS_COLLECTION)
                .document(userId)
                .get()
                .await()
            
            val user = snapshot.toObject(User::class.java)?.copy(userId = snapshot.id)
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Save user settings
     */
    suspend fun saveUserSettings(settings: UserSettings): Result<Unit> {
        return try {
            firestore.collection(USER_SETTINGS_COLLECTION)
                .document(settings.userId)
                .set(settings.toMap())
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get user settings
     */
    suspend fun getUserSettings(userId: String): Result<UserSettings?> {
        return try {
            val snapshot = firestore.collection(USER_SETTINGS_COLLECTION)
                .document(userId)
                .get()
                .await()
            
            val settings = snapshot.toObject(UserSettings::class.java)?.copy(userId = snapshot.id)
            Result.success(settings)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Pin/Unpin a book for user
     */
    suspend fun toggleBookPin(userId: String, bookId: String, isPinned: Boolean): Result<Unit> {
        return try {
            val docRef = firestore.collection(USER_BOOKS_COLLECTION)
                .whereEqualTo("userId", userId)
                .whereEqualTo("bookId", bookId)
                .get()
                .await()
            
            if (docRef.documents.isNotEmpty()) {
                // Update existing record
                val doc = docRef.documents.first()
                doc.reference.update("isPinned", isPinned).await()
            } else {
                // Create new record
                val userBook = UserBook(
                    userId = userId,
                    bookId = bookId,
                    isPinned = isPinned
                )
                firestore.collection(USER_BOOKS_COLLECTION)
                    .add(userBook.toMap())
                    .await()
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update reading progress
     */
    suspend fun updateReadingProgress(
        userId: String, 
        bookId: String, 
        progress: Float, 
        currentSectionId: String
    ): Result<Unit> {
        return try {
            val docRef = firestore.collection(USER_BOOKS_COLLECTION)
                .whereEqualTo("userId", userId)
                .whereEqualTo("bookId", bookId)
                .get()
                .await()
            
            val updates = mapOf(
                "readingProgress" to progress,
                "currentSectionId" to currentSectionId,
                "lastAccessedAt" to com.google.firebase.Timestamp.now()
            )
            
            if (docRef.documents.isNotEmpty()) {
                // Update existing record
                val doc = docRef.documents.first()
                doc.reference.update(updates).await()
            } else {
                // Create new record
                val userBook = UserBook(
                    userId = userId,
                    bookId = bookId,
                    readingProgress = progress,
                    currentSectionId = currentSectionId
                )
                firestore.collection(USER_BOOKS_COLLECTION)
                    .add(userBook.toMap())
                    .await()
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get pinned books for user
     */
    suspend fun getPinnedBooks(userId: String): Result<List<UserBook>> {
        return try {
            val snapshot = firestore.collection(USER_BOOKS_COLLECTION)
                .whereEqualTo("userId", userId)
                .whereEqualTo("isPinned", true)
                .get()
                .await()
            
            val userBooks = snapshot.documents.mapNotNull { doc ->
                doc.toObject(UserBook::class.java)?.copy(id = doc.id)
            }
            Result.success(userBooks)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get recently accessed books for user
     */
    suspend fun getRecentlyAccessedBooks(userId: String, limit: Int = 10): Result<List<UserBook>> {
        return try {
            val snapshot = firestore.collection(USER_BOOKS_COLLECTION)
                .whereEqualTo("userId", userId)
                .orderBy("lastAccessedAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()
            
            val userBooks = snapshot.documents.mapNotNull { doc ->
                doc.toObject(UserBook::class.java)?.copy(id = doc.id)
            }
            Result.success(userBooks)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Add section to favorites
     */
    suspend fun addToFavorites(userId: String, sectionId: String, bookId: String): Result<Unit> {
        return try {
            val favorite = UserFavorite(
                userId = userId,
                sectionId = sectionId,
                bookId = bookId
            )
            firestore.collection(USER_FAVORITES_COLLECTION)
                .add(favorite.toMap())
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Remove section from favorites
     */
    suspend fun removeFromFavorites(userId: String, sectionId: String): Result<Unit> {
        return try {
            val snapshot = firestore.collection(USER_FAVORITES_COLLECTION)
                .whereEqualTo("userId", userId)
                .whereEqualTo("sectionId", sectionId)
                .get()
                .await()
            
            snapshot.documents.forEach { doc ->
                doc.reference.delete().await()
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get user's favorite sections
     */
    suspend fun getFavorites(userId: String): Result<List<UserFavorite>> {
        return try {
            val snapshot = firestore.collection(USER_FAVORITES_COLLECTION)
                .whereEqualTo("userId", userId)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()
            
            val favorites = snapshot.documents.mapNotNull { doc ->
                doc.toObject(UserFavorite::class.java)?.copy(id = doc.id)
            }
            Result.success(favorites)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Check if section is favorited by user
     */
    suspend fun isSectionFavorited(userId: String, sectionId: String): Result<Boolean> {
        return try {
            val snapshot = firestore.collection(USER_FAVORITES_COLLECTION)
                .whereEqualTo("userId", userId)
                .whereEqualTo("sectionId", sectionId)
                .get()
                .await()

            Result.success(snapshot.documents.isNotEmpty())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Create a new tag
     */
    suspend fun createTag(userId: String, name: String, color: String): Result<String> {
        return try {
            val tag = UserTag(
                userId = userId,
                name = name,
                color = color
            )
            val docRef = firestore.collection(USER_TAGS_COLLECTION)
                .add(tag.toMap())
                .await()
            Result.success(docRef.id)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get user's tags
     */
    suspend fun getUserTags(userId: String): Result<List<UserTag>> {
        return try {
            val snapshot = firestore.collection(USER_TAGS_COLLECTION)
                .whereEqualTo("userId", userId)
                .orderBy("name")
                .get()
                .await()

            val tags = snapshot.documents.mapNotNull { doc ->
                doc.toObject(UserTag::class.java)?.copy(tagId = doc.id)
            }
            Result.success(tags)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update tag
     */
    suspend fun updateTag(tagId: String, name: String, color: String): Result<Unit> {
        return try {
            firestore.collection(USER_TAGS_COLLECTION)
                .document(tagId)
                .update(mapOf("name" to name, "color" to color))
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete tag and all its associations
     */
    suspend fun deleteTag(tagId: String): Result<Unit> {
        return try {
            // Delete tag
            firestore.collection(USER_TAGS_COLLECTION)
                .document(tagId)
                .delete()
                .await()

            // Delete all tag-section associations
            val associations = firestore.collection(USER_TAG_SECTIONS_COLLECTION)
                .whereEqualTo("tagId", tagId)
                .get()
                .await()

            associations.documents.forEach { doc ->
                doc.reference.delete().await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Add section to tag
     */
    suspend fun addSectionToTag(userId: String, tagId: String, sectionId: String, bookId: String): Result<Unit> {
        return try {
            val tagSection = UserTagSection(
                userId = userId,
                tagId = tagId,
                sectionId = sectionId,
                bookId = bookId
            )
            firestore.collection(USER_TAG_SECTIONS_COLLECTION)
                .add(tagSection.toMap())
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Remove section from tag
     */
    suspend fun removeSectionFromTag(tagId: String, sectionId: String): Result<Unit> {
        return try {
            val snapshot = firestore.collection(USER_TAG_SECTIONS_COLLECTION)
                .whereEqualTo("tagId", tagId)
                .whereEqualTo("sectionId", sectionId)
                .get()
                .await()

            snapshot.documents.forEach { doc ->
                doc.reference.delete().await()
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get sections for a tag
     */
    suspend fun getSectionsForTag(tagId: String): Result<List<UserTagSection>> {
        return try {
            val snapshot = firestore.collection(USER_TAG_SECTIONS_COLLECTION)
                .whereEqualTo("tagId", tagId)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val tagSections = snapshot.documents.mapNotNull { doc ->
                doc.toObject(UserTagSection::class.java)?.copy(id = doc.id)
            }
            Result.success(tagSections)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Save search query
     */
    suspend fun saveSearchQuery(userId: String, query: String, resultCount: Int): Result<Unit> {
        return try {
            val search = UserSearch(
                userId = userId,
                query = query,
                resultCount = resultCount
            )
            firestore.collection(USER_SEARCHES_COLLECTION)
                .add(search.toMap())
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
