package com.parultrade.indianlawlibrary.data.model

import com.google.firebase.Timestamp
import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp

/**
 * Data class for user's pinned books
 */
data class UserBook(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val bookId: String = "",
    val isPinned: Boolean = false,
    val readingProgress: Float = 0f, // Progress as percentage (0.0 to 1.0)
    val currentSectionId: String = "", // Last read section
    @ServerTimestamp
    val lastAccessedAt: Timestamp? = null,
    @ServerTimestamp
    val createdAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        id = "",
        userId = "",
        bookId = "",
        isPinned = false,
        readingProgress = 0f,
        currentSectionId = "",
        lastAccessedAt = null,
        createdAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "userId" to userId,
            "bookId" to bookId,
            "isPinned" to isPinned,
            "readingProgress" to readingProgress,
            "currentSectionId" to currentSectionId,
            "lastAccessedAt" to lastAccessedAt,
            "createdAt" to createdAt
        )
    }
}

/**
 * Data class for user's favorite sections
 */
data class UserFavorite(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val sectionId: String = "",
    val bookId: String = "",
    @ServerTimestamp
    val createdAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        id = "",
        userId = "",
        sectionId = "",
        bookId = "",
        createdAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "userId" to userId,
            "sectionId" to sectionId,
            "bookId" to bookId,
            "createdAt" to createdAt
        )
    }
}

/**
 * Data class for user-created tags
 */
data class UserTag(
    @DocumentId
    val tagId: String = "",
    val userId: String = "",
    val name: String = "",
    val color: String = "#4294ff", // Default to primary blue
    @ServerTimestamp
    val createdAt: Timestamp? = null,
    @ServerTimestamp
    val updatedAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        tagId = "",
        userId = "",
        name = "",
        color = "#4294ff",
        createdAt = null,
        updatedAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "userId" to userId,
            "name" to name,
            "color" to color,
            "createdAt" to createdAt,
            "updatedAt" to updatedAt
        )
    }
}

/**
 * Data class for associating sections with user tags
 */
data class UserTagSection(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val tagId: String = "",
    val sectionId: String = "",
    val bookId: String = "",
    @ServerTimestamp
    val createdAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        id = "",
        userId = "",
        tagId = "",
        sectionId = "",
        bookId = "",
        createdAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "userId" to userId,
            "tagId" to tagId,
            "sectionId" to sectionId,
            "bookId" to bookId,
            "createdAt" to createdAt
        )
    }
}

/**
 * Data class for user search history
 */
data class UserSearch(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val query: String = "",
    val resultCount: Int = 0,
    @ServerTimestamp
    val searchedAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        id = "",
        userId = "",
        query = "",
        resultCount = 0,
        searchedAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "userId" to userId,
            "query" to query,
            "resultCount" to resultCount,
            "searchedAt" to searchedAt
        )
    }
}
