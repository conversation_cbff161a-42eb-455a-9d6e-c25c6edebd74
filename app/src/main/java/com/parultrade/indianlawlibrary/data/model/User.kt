package com.parultrade.indianlawlibrary.data.model

import com.google.firebase.Timestamp
import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp

/**
 * Data class representing a user profile
 */
data class User(
    @DocumentId
    val userId: String = "",
    val name: String = "",
    val email: String = "",
    val mobileNumber: String = "",
    val address: String = "",
    val profilePhotoUrl: String = "",
    @ServerTimestamp
    val createdAt: Timestamp? = null,
    @ServerTimestamp
    val updatedAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        userId = "",
        name = "",
        email = "",
        mobileNumber = "",
        address = "",
        profilePhotoUrl = "",
        createdAt = null,
        updatedAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "name" to name,
            "email" to email,
            "mobileNumber" to mobileNumber,
            "address" to address,
            "profilePhotoUrl" to profilePhotoUrl,
            "createdAt" to createdAt,
            "updatedAt" to updatedAt
        )
    }
    
    /**
     * Get display name (first name or email if name is empty)
     */
    fun getDisplayName(): String {
        return if (name.isNotBlank()) {
            name.split(" ").firstOrNull() ?: name
        } else {
            email.substringBefore("@")
        }
    }
    
    /**
     * Check if profile is complete
     */
    fun isProfileComplete(): Boolean {
        return name.isNotBlank() && email.isNotBlank()
    }
}

/**
 * Data class for user settings
 */
data class UserSettings(
    @DocumentId
    val userId: String = "",
    val themeMode: ThemeMode = ThemeMode.SYSTEM,
    val fontSize: FontSize = FontSize.MEDIUM,
    val textColor: TextColor = TextColor.BLACK,
    val backgroundColor: BackgroundColor = BackgroundColor.WHITE,
    val brightness: Float = 0.5f,
    val textAlignment: TextAlignment = TextAlignment.LEFT,
    val lineSpacing: Float = 1.5f,
    @ServerTimestamp
    val updatedAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        userId = "",
        themeMode = ThemeMode.SYSTEM,
        fontSize = FontSize.MEDIUM,
        textColor = TextColor.BLACK,
        backgroundColor = BackgroundColor.WHITE,
        brightness = 0.5f,
        textAlignment = TextAlignment.LEFT,
        lineSpacing = 1.5f,
        updatedAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "themeMode" to themeMode.name,
            "fontSize" to fontSize.name,
            "textColor" to textColor.name,
            "backgroundColor" to backgroundColor.name,
            "brightness" to brightness,
            "textAlignment" to textAlignment.name,
            "lineSpacing" to lineSpacing,
            "updatedAt" to updatedAt
        )
    }
}

/**
 * Theme mode options
 */
enum class ThemeMode {
    LIGHT, DARK, SYSTEM
}

/**
 * Font size options
 */
enum class FontSize(val size: Float) {
    SMALL(14f),
    MEDIUM(16f),
    LARGE(18f),
    EXTRA_LARGE(20f)
}

/**
 * Text color options
 */
enum class TextColor(val colorRes: String) {
    BLACK("#212121"),
    DARK_GRAY("#424242"),
    SEPIA("#5D4E37")
}

/**
 * Background color options
 */
enum class BackgroundColor(val colorRes: String) {
    WHITE("#FFFFFF"),
    LIGHT_GRAY("#FAFAFA"),
    SEPIA("#F4F1E8"),
    DARK("#1A1A1A")
}

/**
 * Text alignment options
 */
enum class TextAlignment {
    LEFT, CENTER, JUSTIFY
}
