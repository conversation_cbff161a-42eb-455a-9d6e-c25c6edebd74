package com.parultrade.indianlawlibrary.data.model

import com.google.firebase.Timestamp
import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp

/**
 * Data class for app notifications
 */
data class Notification(
    @DocumentId
    val notificationId: String = "",
    val title: String = "",
    val message: String = "",
    val type: NotificationType = NotificationType.GENERAL,
    val bookId: String? = null, // For book-related notifications
    val sectionId: String? = null, // For section-related notifications
    val isGlobal: Boolean = true, // If true, sent to all users
    val targetUserIds: List<String> = emptyList(), // Specific users if not global
    @ServerTimestamp
    val createdAt: Timestamp? = null,
    val scheduledAt: Timestamp? = null, // For scheduled notifications
    val expiresAt: Timestamp? = null // When notification expires
) {
    // No-argument constructor for Firestore
    constructor() : this(
        notificationId = "",
        title = "",
        message = "",
        type = NotificationType.GENERAL,
        bookId = null,
        sectionId = null,
        isGlobal = true,
        targetUserIds = emptyList(),
        createdAt = null,
        scheduledAt = null,
        expiresAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "title" to title,
            "message" to message,
            "type" to type.name,
            "bookId" to bookId,
            "sectionId" to sectionId,
            "isGlobal" to isGlobal,
            "targetUserIds" to targetUserIds,
            "createdAt" to createdAt,
            "scheduledAt" to scheduledAt,
            "expiresAt" to expiresAt
        )
    }
}

/**
 * Data class for user-specific notification status
 */
data class UserNotification(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val notificationId: String = "",
    val isRead: Boolean = false,
    val isClicked: Boolean = false,
    @ServerTimestamp
    val receivedAt: Timestamp? = null,
    @ServerTimestamp
    val readAt: Timestamp? = null,
    @ServerTimestamp
    val clickedAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        id = "",
        userId = "",
        notificationId = "",
        isRead = false,
        isClicked = false,
        receivedAt = null,
        readAt = null,
        clickedAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "userId" to userId,
            "notificationId" to notificationId,
            "isRead" to isRead,
            "isClicked" to isClicked,
            "receivedAt" to receivedAt,
            "readAt" to readAt,
            "clickedAt" to clickedAt
        )
    }
}

/**
 * Data class for user notification preferences
 */
data class UserNotificationPreferences(
    @DocumentId
    val userId: String = "",
    val newBooksEnabled: Boolean = true,
    val amendmentsEnabled: Boolean = true,
    val generalUpdatesEnabled: Boolean = true,
    val pushNotificationsEnabled: Boolean = true,
    val emailNotificationsEnabled: Boolean = false,
    @ServerTimestamp
    val updatedAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        userId = "",
        newBooksEnabled = true,
        amendmentsEnabled = true,
        generalUpdatesEnabled = true,
        pushNotificationsEnabled = true,
        emailNotificationsEnabled = false,
        updatedAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "newBooksEnabled" to newBooksEnabled,
            "amendmentsEnabled" to amendmentsEnabled,
            "generalUpdatesEnabled" to generalUpdatesEnabled,
            "pushNotificationsEnabled" to pushNotificationsEnabled,
            "emailNotificationsEnabled" to emailNotificationsEnabled,
            "updatedAt" to updatedAt
        )
    }
}

/**
 * Notification types
 */
enum class NotificationType(val displayName: String) {
    NEW_BOOK("नई पुस्तक"),
    AMENDMENT("संशोधन"),
    GENERAL("सामान्य"),
    SYSTEM("सिस्टम"),
    FEATURE_UPDATE("नई सुविधा")
}

/**
 * Data class for subscription management
 */
data class Subscription(
    @DocumentId
    val subscriptionId: String = "",
    val userId: String = "",
    val type: SubscriptionType = SubscriptionType.FREE,
    val startDate: Timestamp? = null,
    val endDate: Timestamp? = null,
    val isActive: Boolean = true,
    val features: List<String> = emptyList(),
    @ServerTimestamp
    val createdAt: Timestamp? = null,
    @ServerTimestamp
    val updatedAt: Timestamp? = null
) {
    // No-argument constructor for Firestore
    constructor() : this(
        subscriptionId = "",
        userId = "",
        type = SubscriptionType.FREE,
        startDate = null,
        endDate = null,
        isActive = true,
        features = emptyList(),
        createdAt = null,
        updatedAt = null
    )
    
    /**
     * Convert to map for Firestore operations
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "userId" to userId,
            "type" to type.name,
            "startDate" to startDate,
            "endDate" to endDate,
            "isActive" to isActive,
            "features" to features,
            "createdAt" to createdAt,
            "updatedAt" to updatedAt
        )
    }
}

/**
 * Subscription types
 */
enum class SubscriptionType {
    FREE, PREMIUM, PROFESSIONAL
}
